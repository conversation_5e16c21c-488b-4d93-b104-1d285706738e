import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../domain/entities/food_entry.dart';
import '../../domain/entities/nutrition_data.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';

class NutritionEditDialog extends StatefulWidget {
  final FoodEntry foodEntry;

  const NutritionEditDialog({super.key, required this.foodEntry});

  @override
  State<NutritionEditDialog> createState() => _NutritionEditDialogState();
}

class _NutritionEditDialogState extends State<NutritionEditDialog> {
  late TextEditingController _foodNameController;
  late TextEditingController _caloriesController;
  late TextEditingController _proteinController;
  late TextEditingController _carbsController;
  late TextEditingController _fatController;

  @override
  void initState() {
    super.initState();
    _foodNameController = TextEditingController(text: widget.foodEntry.name);
    _caloriesController = TextEditingController(
        text: widget.foodEntry.nutritionData.calories.toString());
    _proteinController = TextEditingController(
        text: widget.foodEntry.nutritionData.protein.toString());
    _carbsController = TextEditingController(
        text: widget.foodEntry.nutritionData.carbs.toString());
    _fatController = TextEditingController(
        text: widget.foodEntry.nutritionData.fats.toString());
  }

  @override
  void dispose() {
    _foodNameController.dispose();
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbsController.dispose();
    _fatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Nutrition Details'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _foodNameController,
              decoration: const InputDecoration(labelText: 'Food Name'),
              keyboardType: TextInputType.text,
            ),
            TextFormField(
              controller: _caloriesController,
              decoration: const InputDecoration(labelText: 'Calories (kcal)'),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
            ),
            TextFormField(
              controller: _proteinController,
              decoration: const InputDecoration(labelText: 'Protein (g)'),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
            ),
            TextFormField(
              controller: _carbsController,
              decoration: const InputDecoration(labelText: 'Carbohydrates (g)'),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
            ),
            TextFormField(
              controller: _fatController,
              decoration: const InputDecoration(labelText: 'Fat (g)'),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
            ),
          ],
        ),
      ),
      actions: [
        CustomButton(
          text: 'Cancel',
          isOutlined: true,
          onPressed: () {
            Navigator.of(context).pop(); // Dismiss dialog
          },
        ),
        CustomButton(
          text: 'Save',
          onPressed: () {
            final updatedFoodEntry = widget.foodEntry.copyWith(
              name: _foodNameController.text,
              nutritionData: NutritionData(
                calories: double.tryParse(_caloriesController.text) ?? 0.0,
                protein: double.tryParse(_proteinController.text) ?? 0.0,
                carbs: double.tryParse(_carbsController.text) ?? 0.0,
                fats: double.tryParse(_fatController.text) ?? 0.0,
              ),
            );
            Navigator.of(context).pop(updatedFoodEntry); // Return updated entry
          },
        ),
      ],
    );
  }
}