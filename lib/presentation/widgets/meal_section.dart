import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_card.dart';
import '../../domain/entities/food_entry.dart';
import 'food_detail_bottom_sheet.dart';

class MealSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final String mealType;
  final List<FoodEntry> entries;

  const MealSection({
    super.key,
    required this.title,
    required this.icon,
    required this.mealType,
    required this.entries,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final totalCalories = entries.fold<double>(
      0,
      (sum, entry) => sum + entry.nutritionData.calories,
    );

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: theme.colorScheme.primary, size: 24),

              const SizedBox(width: AppConstants.smallPadding),

              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              Text(
                '${totalCalories.toInt()} cal',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(width: AppConstants.smallPadding),

              IconButton(
                icon: const Icon(Icons.add),
                onPressed: () => _showAddFoodOptions(context),
                iconSize: 20,
              ),
            ],
          ),

          if (entries.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),

            ...entries.map((entry) => _buildFoodEntryItem(context, entry)),
          ] else ...[
            const SizedBox(height: AppConstants.smallPadding),

            Text(
              'No ${title.toLowerCase()} logged yet',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),

            const SizedBox(height: AppConstants.smallPadding),

            TextButton.icon(
              onPressed: () => _showAddFoodOptions(context),
              icon: const Icon(Icons.add),
              label: Text('Add ${title.toLowerCase()}'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFoodEntryItem(BuildContext context, FoodEntry entry) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: InkWell(
        onTap: () => _showFoodDetailBottomSheet(context, entry),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(
              alpha: 0.3,
            ),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Row(
            children: [
              // Food icon or image
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: entry.imagePath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          entry.imagePath!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.restaurant,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.restaurant,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    if (entry.brand != null) ...[
                      Text(
                        entry.brand!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],

                    Text(
                      '${entry.servingSize} ${entry.servingUnit}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${entry.nutritionData.calories.toInt()} cal',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),

                  if (entry.isAnalyzedByAI) ...[
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          size: 12,
                          color: theme.colorScheme.secondary,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          'AI',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddFoodOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Add to $title',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              subtitle: const Text('Analyze food with AI'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  AppRoutes.camera,
                  arguments: {'mealType': mealType},
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Manual Entry'),
              subtitle: const Text('Enter nutrition manually'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  AppRoutes.addFood,
                  arguments: {'mealType': mealType},
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFoodDetailBottomSheet(BuildContext context, FoodEntry entry) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FoodDetailBottomSheet(
        foodEntry: entry,
        onSave: () {
          // Handle save action if needed
        },
      ),
    );
  }
}
