import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';

import '../../core/widgets/custom_button.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';

class GoalsPage extends StatefulWidget {
  const GoalsPage({super.key});

  @override
  State<GoalsPage> createState() => _GoalsPageState();
}

class _GoalsPageState extends State<GoalsPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _caloriesController;
  late TextEditingController _proteinController;
  late TextEditingController _carbsController;
  late TextEditingController _fatsController;
  late TextEditingController _fiberController;
  late TextEditingController _sugarController;

  @override
  void initState() {
    super.initState();
    _caloriesController = TextEditingController();
    _proteinController = TextEditingController();
    _carbsController = TextEditingController();
    _fatsController = TextEditingController();
    _fiberController = TextEditingController();
    _sugarController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCurrentGoals();
    });
  }

  @override
  void dispose() {
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbsController.dispose();
    _fatsController.dispose();
    _fiberController.dispose();
    _sugarController.dispose();
    super.dispose();
  }

  void _loadCurrentGoals() {
    final nutritionProvider = context.read<NutritionProvider>();
    final goals = nutritionProvider.nutritionalGoals;

    if (goals != null) {
      _caloriesController.text = goals.dailyCalories.toInt().toString();
      _proteinController.text = goals.dailyProtein.toInt().toString();
      _carbsController.text = goals.dailyCarbs.toInt().toString();
      _fatsController.text = goals.dailyFats.toInt().toString();
      _fiberController.text = goals.dailyFiber.toInt().toString();
      _sugarController.text = goals.dailySugar.toInt().toString();
    } else {
      // Set default values
      _caloriesController.text = AppConstants.defaultCaloriesPerDay
          .toInt()
          .toString();
      _proteinController.text = AppConstants.defaultProteinPerDay
          .toInt()
          .toString();
      _carbsController.text = AppConstants.defaultCarbsPerDay
          .toInt()
          .toString();
      _fatsController.text = AppConstants.defaultFatsPerDay.toInt().toString();
      _fiberController.text = AppConstants.defaultFiberPerDay
          .toInt()
          .toString();
      _sugarController.text = AppConstants.defaultSugarPerDay
          .toInt()
          .toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nutrition Goals'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF4A90E2), Color(0xFF5CB3F5)],
            begin: Alignment.topCenter,
            end: Alignment.center,
          ),
        ),
        child: Column(
          children: [
            // Header section
            Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: const Icon(
                      Icons.track_changes,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Set Your Goals',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Customize your daily nutrition targets',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            // Content section
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Daily Targets',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 24),

                        // Calories
                        _buildGoalField(
                          controller: _caloriesController,
                          label: 'Calories',
                          unit: 'kcal',
                          icon: Icons.local_fire_department,
                          color: const Color(0xFF4A90E2),
                        ),

                        const SizedBox(height: 20),

                        // Macronutrients section
                        Text(
                          'Macronutrients',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: _buildGoalField(
                                controller: _proteinController,
                                label: 'Protein',
                                unit: 'g',
                                icon: Icons.fitness_center,
                                color: const Color(0xFF5CB3F5),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildGoalField(
                                controller: _carbsController,
                                label: 'Carbs',
                                unit: 'g',
                                icon: Icons.grain,
                                color: const Color(0xFF7B68EE),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        _buildGoalField(
                          controller: _fatsController,
                          label: 'Fats',
                          unit: 'g',
                          icon: Icons.opacity,
                          color: const Color(0xFFFF6B6B),
                        ),

                        const SizedBox(height: 20),

                        // Other nutrients section
                        Text(
                          'Other Nutrients',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: _buildGoalField(
                                controller: _fiberController,
                                label: 'Fiber',
                                unit: 'g',
                                icon: Icons.eco,
                                color: const Color(0xFF4CAF50),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildGoalField(
                                controller: _sugarController,
                                label: 'Sugar',
                                unit: 'g',
                                icon: Icons.cake,
                                color: const Color(0xFFFF9800),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Save button
                        CustomButton(
                          text: 'Save Goals',
                          onPressed: _saveGoals,
                          width: double.infinity,
                          icon: Icons.check,
                        ),

                        const SizedBox(height: 16),

                        // Reset to defaults button
                        CustomButton(
                          text: 'Reset to Defaults',
                          onPressed: _resetToDefaults,
                          width: double.infinity,
                          isOutlined: true,
                          icon: Icons.refresh,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalField({
    required TextEditingController controller,
    required String label,
    required String unit,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 18),
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: color, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a value';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 8),
              Text(
                unit,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _saveGoals() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    if (appProvider.currentUser == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('User not found')));
      return;
    }

    try {
      // Here you would typically save to your repository
      // For now, we'll just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Goals saved successfully!'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );

      // Reload nutrition data
      await nutritionProvider.loadDailyNutrition(appProvider.currentUser!.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving goals: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _resetToDefaults() {
    _caloriesController.text = AppConstants.defaultCaloriesPerDay
        .toInt()
        .toString();
    _proteinController.text = AppConstants.defaultProteinPerDay
        .toInt()
        .toString();
    _carbsController.text = AppConstants.defaultCarbsPerDay.toInt().toString();
    _fatsController.text = AppConstants.defaultFatsPerDay.toInt().toString();
    _fiberController.text = AppConstants.defaultFiberPerDay.toInt().toString();
    _sugarController.text = AppConstants.defaultSugarPerDay.toInt().toString();
  }
}
