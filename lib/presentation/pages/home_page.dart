import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/utils/date_utils.dart';
import '../providers/app_provider.dart';
import '../providers/nutrition_provider.dart';
import '../widgets/meal_section.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    if (appProvider.currentUser != null) {
      await nutritionProvider.loadDailyNutrition(appProvider.currentUser!.id);
      await nutritionProvider.loadRecentEntries(appProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<NutritionProvider>(
        builder: (context, nutritionProvider, child) {
          if (nutritionProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (nutritionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Error loading data',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    nutritionProvider.error!,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  CustomButton(
                    text: 'Retry',
                    onPressed: _loadData,
                    icon: Icons.refresh,
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                // Modern header with gradient
                _buildModernHeader(context, nutritionProvider),

                // Content
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date selector
                        _buildDateSelector(nutritionProvider),

                        const SizedBox(height: 24),

                        // Nutrition progress with circular design
                        _buildModernNutritionProgress(nutritionProvider),

                        const SizedBox(height: 24),

                        // Meals section
                        _buildMealsSection(nutritionProvider),

                        const SizedBox(height: 100), // Space for bottom nav
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateSelector(NutritionProvider nutritionProvider) {
    final theme = Theme.of(context);
    final selectedDate = nutritionProvider.selectedDate;

    return CustomCard(
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () =>
                _changeDate(selectedDate.subtract(const Duration(days: 1))),
          ),

          Expanded(
            child: GestureDetector(
              onTap: () => _selectDate(context),
              child: Column(
                children: [
                  Text(
                    AppDateUtils.formatDateForDisplay(selectedDate),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    AppDateUtils.getRelativeDateString(selectedDate),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),

          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed:
                selectedDate.isBefore(
                  DateTime.now().subtract(const Duration(days: 1)),
                )
                ? () => _changeDate(selectedDate.add(const Duration(days: 1)))
                : null,
          ),
        ],
      ),
    );
  }

  Widget _buildMealsSection(NutritionProvider nutritionProvider) {
    final mealGroups = nutritionProvider.mealGroups;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Meals',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Breakfast',
          icon: Icons.free_breakfast,
          mealType: 'breakfast',
          entries: mealGroups['breakfast'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Lunch',
          icon: Icons.lunch_dining,
          mealType: 'lunch',
          entries: mealGroups['lunch'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Dinner',
          icon: Icons.dinner_dining,
          mealType: 'dinner',
          entries: mealGroups['dinner'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Snacks',
          icon: Icons.cookie,
          mealType: 'snack',
          entries: mealGroups['snack'] ?? [],
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: context.read<NutritionProvider>().selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      _changeDate(date);
    }
  }

  void _changeDate(DateTime date) {
    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    nutritionProvider.selectDate(date);

    if (appProvider.currentUser != null) {
      nutritionProvider.loadDailyNutrition(
        appProvider.currentUser!.id,
        date: date,
      );
    }
  }

  Widget _buildModernHeader(
    BuildContext context,
    NutritionProvider nutritionProvider,
  ) {
    final user = context.watch<AppProvider>().currentUser;
    final currentData = nutritionProvider.currentDayData;
    final goals = nutritionProvider.nutritionalGoals;

    int caloriesLeft = 0;
    if (currentData != null && goals != null) {
      caloriesLeft =
          (goals.dailyCalories - currentData.dailyLog.totalNutrition.calories)
              .round();
    }

    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF4A90E2), Color(0xFF5CB3F5)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Daily Meal Plan',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            'Hello, ${user?.name ?? 'User'}',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                          ),
                        ],
                      ),
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 20,
                          ),
                          onPressed: () =>
                              Navigator.pushNamed(context, AppRoutes.profile),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        children: [
                          Text(
                            caloriesLeft.toString(),
                            style: Theme.of(context).textTheme.headlineLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            'KCALS LEFT',
                            style: Theme.of(context).textTheme.labelMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  letterSpacing: 1.2,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernNutritionProgress(NutritionProvider nutritionProvider) {
    final currentData = nutritionProvider.currentDayData;
    final goals = nutritionProvider.nutritionalGoals;
    final progress = nutritionProvider.progressPercentages;

    if (currentData == null || goals == null) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: Text('Set up your nutrition goals to track progress'),
        ),
      );
    }

    final totalNutrition = currentData.dailyLog.totalNutrition;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Circular progress indicator
          SizedBox(
            width: 120,
            height: 120,
            child: Stack(
              children: [
                // Background circle
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[100],
                  ),
                ),
                // Progress circle
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: (progress['calories'] ?? 0.0).clamp(0.0, 1.0),
                    strokeWidth: 8,
                    backgroundColor: Colors.grey[200],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Color(0xFF4A90E2),
                    ),
                  ),
                ),
                // Center content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        totalNutrition.calories.toInt().toString(),
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF4A90E2),
                            ),
                      ),
                      Text(
                        'kcal',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          // Macros breakdown
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildMacroItem(
                'Carbs',
                '${totalNutrition.carbs.toInt()}g',
                '${((progress['carbs'] ?? 0.0) * 100).toInt()}%',
                const Color(0xFF7B68EE),
              ),
              _buildMacroItem(
                'Protein',
                '${totalNutrition.protein.toInt()}g',
                '${((progress['protein'] ?? 0.0) * 100).toInt()}%',
                const Color(0xFF5CB3F5),
              ),
              _buildMacroItem(
                'Fat',
                '${totalNutrition.fats.toInt()}g',
                '${((progress['fats'] ?? 0.0) * 100).toInt()}%',
                const Color(0xFFFF6B6B),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMacroItem(
    String label,
    String value,
    String percentage,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        Text(
          percentage,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
