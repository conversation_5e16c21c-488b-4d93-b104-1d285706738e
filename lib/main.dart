import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'core/constants/app_constants.dart';
import 'core/routes/route_generator.dart';
import 'core/routes/app_routes.dart';
import 'data/datasources/database_helper.dart';
import 'data/datasources/gemini_api_service.dart';
import 'data/repositories/user_profile_repository_impl.dart';
import 'data/repositories/food_entry_repository_impl.dart';
import 'data/repositories/nutritional_goals_repository_impl.dart';
import 'domain/usecases/create_user_profile_usecase.dart';
import 'domain/usecases/analyze_food_image_usecase.dart';
import 'domain/usecases/get_daily_nutrition_usecase.dart';
import 'presentation/providers/app_provider.dart';
import 'presentation/providers/nutrition_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  final databaseHelper = DatabaseHelper();
  final httpClient = http.Client();

  // Initialize repositories
  final userProfileRepository = UserProfileRepositoryImpl(databaseHelper);
  final foodEntryRepository = FoodEntryRepositoryImpl(databaseHelper);
  final nutritionalGoalsRepository = NutritionalGoalsRepositoryImpl(
    databaseHelper,
  );

  // Initialize Gemini API service
  final geminiApiService = GeminiApiService(
    client: httpClient,
    apiKey: AppConstants.geminiApiKey,
  );

  // Initialize use cases
  final createUserProfileUseCase = CreateUserProfileUseCase(
    userProfileRepository,
    nutritionalGoalsRepository,
  );

  final getDailyNutritionUseCase = GetDailyNutritionUseCase(
    foodEntryRepository,
    nutritionalGoalsRepository,
  );

  final analyzeFoodImageUseCase = AnalyzeFoodImageUseCase(geminiApiService);

  runApp(
    NutriAIApp(
      sharedPreferences: sharedPreferences,
      userProfileRepository: userProfileRepository,
      foodEntryRepository: foodEntryRepository,
      nutritionalGoalsRepository: nutritionalGoalsRepository,
      createUserProfileUseCase: createUserProfileUseCase,
      getDailyNutritionUseCase: getDailyNutritionUseCase,
      analyzeFoodImageUseCase: analyzeFoodImageUseCase,
      httpClient: httpClient,
    ),
  );
}

class NutriAIApp extends StatelessWidget {
  final SharedPreferences sharedPreferences;
  final UserProfileRepositoryImpl userProfileRepository;
  final FoodEntryRepositoryImpl foodEntryRepository;
  final NutritionalGoalsRepositoryImpl nutritionalGoalsRepository;
  final CreateUserProfileUseCase createUserProfileUseCase;
  final GetDailyNutritionUseCase getDailyNutritionUseCase;
  final AnalyzeFoodImageUseCase analyzeFoodImageUseCase;
  final http.Client httpClient;

  const NutriAIApp({
    super.key,
    required this.sharedPreferences,
    required this.userProfileRepository,
    required this.foodEntryRepository,
    required this.nutritionalGoalsRepository,
    required this.createUserProfileUseCase,
    required this.getDailyNutritionUseCase,
    required this.analyzeFoodImageUseCase,
    required this.httpClient,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AppProvider(userProfileRepository, sharedPreferences),
        ),
        ChangeNotifierProvider(
          create: (_) =>
              NutritionProvider(getDailyNutritionUseCase, foodEntryRepository),
        ),
        Provider<CreateUserProfileUseCase>.value(
          value: createUserProfileUseCase,
        ),
        Provider<AnalyzeFoodImageUseCase>.value(value: analyzeFoodImageUseCase),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: _buildTheme(),
        initialRoute: AppRoutes.splash,
        onGenerateRoute: RouteGenerator.generateRoute,
        debugShowCheckedModeBanner: false,
      ),
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF4A90E2), // Modern blue primary
        secondary: Color(0xFF5CB3F5), // Light blue secondary
        tertiary: Color(0xFF7B68EE), // Purple accent
        surface: Color(0xFFFAFAFA), // Light gray surface
        surfaceContainer: Color(0xFFFFFFFF), // White container
        surfaceContainerHighest: Color(0xFFF5F5F5), // Light gray
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Color(0xFF1A1A1A), // Dark text
        onSurfaceVariant: Color(0xFF666666), // Gray text
        outline: Color(0xFFE0E0E0), // Light border
        error: Color(0xFFE53E3E), // Red error
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          side: const BorderSide(color: Color(0xFF4A90E2), width: 1.5),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFFF8F9FA),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF4A90E2), width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: Color(0xFF4A90E2),
        unselectedItemColor: Color(0xFF9E9E9E),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Color(0xFF1A1A1A),
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Color(0xFF1A1A1A),
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: Color(0xFF1A1A1A),
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Color(0xFF1A1A1A),
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: Color(0xFF666666),
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Color(0xFF666666),
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Color(0xFF666666),
        ),
        labelSmall: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Color(0xFF666666),
        ),
      ),
    );
  }
}
